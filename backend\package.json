{"name": "driving-exam-backend", "version": "1.0.0", "description": "驾考助手后端 - Node.js版本", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "lint": "eslint . --fix"}, "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^6.9.7", "pdf-lib": "^1.17.1", "resend": "^3.5.0", "sharp": "^0.33.0", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^6.3.3"}, "keywords": ["driving", "exam", "nodejs", "express", "mysql"], "author": "Developer", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}