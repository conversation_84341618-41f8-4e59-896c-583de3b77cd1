#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/bin/node_modules:/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/node_modules:/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/node_modules:/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/uuid@9.0.1/node_modules:/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/bin/node_modules:/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/node_modules:/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/node_modules:/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/uuid@9.0.1/node_modules:/proc/cygdrive/e/7mouthMission/DriveEasy-Passv0.1/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/uuid@9.0.1/node_modules/uuid/dist/bin/uuid" "$@"
else
  exec node  "$basedir/../.pnpm/uuid@9.0.1/node_modules/uuid/dist/bin/uuid" "$@"
fi
